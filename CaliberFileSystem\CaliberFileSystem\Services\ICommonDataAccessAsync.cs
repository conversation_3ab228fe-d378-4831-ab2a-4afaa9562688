using System.Data;
using System.Dynamic;

namespace Caliber.Application
{
    public interface ICommonDataAccessAsync : IDisposable
    {
        Task WriteDataAsync(CommandType commandType, string commandText, object inputParams);
        Task<object> WriteDataAsync(CommandType commandType, string commandText, object inputParams, object outputParams);
        Task<object> WriteDataAsync(CommandType commandType, string commandText, List<object> inputParamsList, object outputParams);
        Task<object> WriteDataAsync(CommandType commandType, string commandText, object inputParamsList, Dictionary<string, object> runtimeParams, object outputParams);
        Task<object> WriteDataAsync(CommandType commandType, string commandText, List<object> inputParamsList, Dictionary<string, object> runtimeParams, object outputParams);
        Task<T> ReadDataAsync<T>(CommandType commandType, string commandText, object param);
        Task<List<T>> ReadDataListAsync<T>(CommandType commandType, string commandText, object param);
        Task<List<T>> ReadDataListAsync<T>(CommandType commandType, string commandText, object param, Dictionary<string, object> RuntimeParams);
        Task<List<T>> ReadDataListAsync<T>(CommandType commandType, string commandText, List<object> param);
        Task<(T, TQ, List<TU>)> ReadDataAsync<T, TQ, TU>(CommandType commandType, string commandText, object param);
        Task<(List<T>, List<TQ>, List<TU>)> ReadDataListAsync<T, TQ, TU>(CommandType commandType, string commandText, object param);
        Task<(List<T>, List<TQ>)> ReadDataListAsync<T, TQ>(CommandType commandType, string commandText, object param);
        Task<(List<T>, int)> ReadDataListAsync<T>(CommandType commandType, string commandText, object @params, int pageNo, int pageSize);
        Task<(List<T>, int)> ReadDataListAsync<T>(CommandType commandType, string commandText, List<object> param, int pageNo, int pageSize);
        Task ExtWriteDataAsync(string conStr, CommandType commandType, string commandText, object inputParams, Dictionary<string, object> runtimeParams);
        Task<object> ExtWriteDataAsync(string conStr, CommandType commandType, string commandText, object inputParams, Dictionary<string, object> runtimeParams, object outputParams);
        Task<object> ExtWriteDataAsync(string conStr, CommandType commandType, string commandText, List<object> inputParamsList, Dictionary<string, object> RuntimeParams, object outputParams);
        Task<IEnumerable<dynamic>> ReadAsIEnumerableAsync(CommandType commandType, string commandText, object param);
        Task<IEnumerable<ExpandoObject>> ReadAsIEnumerableAsync(CommandType commandType, string commandText);
        Task<IEnumerable<ExpandoObject>> ReadDataAsIEnumerableAsync(CommandType commandType, string commandText, object param, Dictionary<string, object> RuntimeParams);
        Task<Exception> ExceptionDataLogAsync(List<object> inputParamsList, object outputParams, string commandText, string conStr, Exception ex);
        dynamic ToExpandoObject(object value);
    }
}