﻿using CaliberFileSystem.Models;
using IronPdf.Rendering;
using System.Web;

namespace CaliberFileSystem.Utilities
{
    /// <summary>
    /// Interface for generating dossier PDFs.
    /// </summary>
    public interface IDossierGenerator
    {
        /// <summary>
        /// Converts HTML input to a PDF and returns the generated PDF details.
        /// </summary>
        /// <param name="TestDataReportsModel">Data for test reports.</param>
        /// <param name="EDetReportModel">Data for EDet reports.</param>
        /// <param name="i">Flag to determine the source of HTML content by iteration.</param>
        /// <param name="dataVal">Dynamic data object containing rendering options.</param>
        /// <returns>PDF merge settings containing generated PDF details.</returns>
        Task<pdfFiles> HTMLToPDF(dynamic HTMLDataModel, string i, dynamic dataVal = null);
    }

    /// <summary>
    /// Implementation of IDossierGenerator for generating dossier PDFs.
    /// </summary>
    public class DossierGenerator : IDossierGenerator
    {
        /// <inheritdoc />
        public async Task<pdfFiles> HTMLToPDF(dynamic HTMLDataModel,string i, dynamic dataVal)
        {
            try
            {
                // Initialize variables for HTML content
                string htmlTextHeader, htmlTextFooter, htmlText;

                // Determine the HTML content based on the input flag 'i'
                if (i == "1")
                {
                    htmlTextHeader = HttpUtility.HtmlDecode(HTMLDataModel.GeneratedHeaderHtml);
                    htmlTextFooter = HttpUtility.HtmlDecode(HTMLDataModel.GeneratedFormatHtml);
                    htmlText = HttpUtility.HtmlDecode(HTMLDataModel.GeneratedBodyHtml) +
                               HttpUtility.HtmlDecode(HTMLDataModel.GeneratedFooterHtml);
                }
                else if (i == "3")
                {
                    htmlTextHeader = HttpUtility.HtmlDecode(HTMLDataModel.GeneratedHeaderHtml);
                    htmlTextFooter = HttpUtility.HtmlDecode(HTMLDataModel.GeneratedFormatHtml);
                    htmlText = HttpUtility.HtmlDecode(HTMLDataModel.GeneratedBodyHtml) +
                               HttpUtility.HtmlDecode(HTMLDataModel.GeneratedFooterHtml);
                }
                else
                {
                    htmlTextHeader = HttpUtility.HtmlDecode(HTMLDataModel.GeneratedHeaderHtml);
                    htmlTextFooter = HttpUtility.HtmlDecode(HTMLDataModel.GeneratedFooterHtml);
                    htmlText = HttpUtility.HtmlDecode(HTMLDataModel.GeneratedBodyHtml);
                }

                // Replace placeholders in the footer HTML
                htmlTextFooter = htmlTextFooter
                    .Replace(
                        HttpUtility.HtmlDecode("#PageX#"),
                        HttpUtility.HtmlDecode("{page}"))
                    .Replace(
                        HttpUtility.HtmlDecode("#PageY#"),
                        HttpUtility.HtmlDecode("{total-pages}"));

                // Configure the PDF renderer
                var renderer = new ChromePdfRenderer
                {
                    RenderingOptions =
                    {
                        MarginTop = Convert.ToInt32(dataVal.MarginTop),
                        MarginBottom = Convert.ToInt32(dataVal.MarginBottom),
                        MarginLeft = Convert.ToInt32(dataVal.MarginLeft),
                        MarginRight = Convert.ToInt32(dataVal.MarginRight),
                        UseMarginsOnHeaderAndFooter = UseMargins.All,
                        Timeout = 500,
                        EnableJavaScript = false,
                        PaperOrientation = Convert.ToInt32(dataVal.Orientation) != 1
                            ? PdfPaperOrientation.Landscape
                            : PdfPaperOrientation.Portrait,
                        HtmlHeader = new HtmlHeaderFooter
                        {
                            HtmlFragment = htmlTextHeader,
                            MaxHeight = HtmlHeaderFooter.FragmentHeight
                        },
                        HtmlFooter = new HtmlHeaderFooter
                        {
                            HtmlFragment = htmlTextFooter,
                            MaxHeight = HtmlHeaderFooter.FragmentHeight
                        }
                    }
                };

                // Render the PDF
                var pdf = await Task.Run(() => renderer.RenderHtmlAsPdf(htmlText));
                new pdfFiles
                {
                    FileData = pdf.BinaryData,
                    FileExtension = ".pdf",
                    FileName = "Dossier.Pdf"
                };
                // Create and return the PDF merge settings
                return new pdfFiles
                {
                    FileData = pdf.BinaryData,
                    FileExtension = ".pdf",
                    FileName = "Dossier.Pdf"
                };
            }
            catch (Exception ex)
            {
                // Log the exception and rethrow
                Console.WriteLine($"Error in HTMLToPDF: {ex.Message}");
                throw new InvalidOperationException("Failed to generate PDF.", ex);
            }
        }
    }
}
