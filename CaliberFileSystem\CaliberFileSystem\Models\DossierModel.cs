﻿namespace CaliberFileSystem.Models
{
    public class HTMLDataModel
    {
        public string GeneratedHeaderHtml { get; set; }
        public string GeneratedBodyHtml { get; set; }
        public string GeneratedFooterHtml { get; set; }
        public string GeneratedFormatHtml { get; set; }
    }
    public class PdfMergeSettingsModel
    {
        public List<pdfFiles> pdfFiles { get; set; } = new List<pdfFiles>();
    }
    public class pdfFiles
    {
        public byte[] FileData { get; set; }

        public string FileExtension { get; set; }

        public string FileName { get; set; }

    }
    public class DataValModel
    {
        public int Orientation { get; set; }
        public int MarginTop { get; set; }
        public int MarginBottom { get; set; }
        public int MarginLeft { get; set; }
        public int MarginRight { get; set; }
        public string DriverNameVal { get; set; }
    }

    public class GenerateDossierRequest
    {
        public HTMLDataModel HTMLDataModel { get; set; }
        public string i { get; set; }
        public DataValModel dynamicData { get; set; }
    }

}
