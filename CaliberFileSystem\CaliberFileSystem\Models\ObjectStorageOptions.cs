﻿using BlobHelper;
using System.Runtime.Serialization;

namespace CaliberFileSystem.Models
{
    /// <summary>
    /// Represents the configuration options for object storage.
    /// </summary>
    public class ObjectStorageOptions
    {
        /// <summary>
        /// Gets or sets a value indicating whether object storage is enabled or disabled.
        /// </summary>
        public bool Enabled { get; set; }

        /// <summary>
        /// Gets or sets the provider for object storage.
        /// </summary>
        public string Provider { get; set; }

        /// <summary>
        /// Gets or sets the settings specific to Azure object storage.
        /// </summary>
        public AzureSettings AzureSettings { get; set; }

        /// <summary>
        /// Gets or sets the settings specific to AWS object storage.
        /// </summary>
        public AwsSettings AwsSettings { get; set; }

        /// <summary>
        /// Gets or sets the settings specific to MinIO object storage.
        /// </summary>
        public AwsSettings MinIOSettings { get; set; }
    }

    public enum FileStorageType
    {
        //
        // Summary:
        //     Amazon Simple Storage Service.
        [EnumMember(Value = "AwsS3")]
        AwsS3,
        //
        // Summary:
        //     Microsoft Azure BLOB Storage Service.
        [EnumMember(Value = "Azure")]
        Azure,
        //
        // Summary:
        //     Local filesystem/disk storage.
        [EnumMember(Value = "Disk")]
        Disk,
        //
        // Summary:
        //     Local filesystem/disk storage.
        [EnumMember(Value = "Sharepoint")]
        Sharepoint,
        //
        // Summary:
        //     Database storage.
        [EnumMember(Value = "DataBase")]
        DataBase,
    }

}
