﻿using CaliberFileSystem.Models;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using Newtonsoft.Json;

namespace CaliberFileSystemXUnitTests
{
    public class FileStorageControllerTests
    {
        private readonly HttpClient _httpClient;

        public FileStorageControllerTests()
        {
            // Initialize the HttpClient with the base address of your application
            _httpClient = new HttpClient
            {
                BaseAddress = new Uri("http://localhost/CaliberFileStorage/") // Replace with your application's base address
            };
        }
        [Fact]
        public async Task FileStorage_Post_ReturnsOkResult()
        {
            var user = new
            {
                UserName = "<EMAIL>",
                Password = "P@ssword"
            };

            // Act
            var response = await _httpClient.PostAsJsonAsync("security/v1/getToken", user);

            var FileData = await File.ReadAllBytesAsync(@"C:\DocFiles\This is for verification.docx");
            var PdfFileData = await File.ReadAllBytesAsync(@"C:\DocFiles\This is for verification.pdf");

            // Assert
            response.EnsureSuccessStatusCode();
            var token = await response.Content.ReadAsStringAsync();

            if (token != "")
            {
                token = token.Substring(1, token.Length - 2);
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                var lmsDmsIndex = new LmsDmsIndex
                {
                    DmsDocUcode = new Guid().ToString(),
                    DmsDocSourceId = 1,
                    DmsDocSourceTypeId = 1,
                    DmsDocVno = 1,
                    LmsEnbEnbId = 1,
                    LmsEnbEnbSecId = 1,
                    FileData = FileData, 
                    DmsDocFileExt = ".pdf",
                    DmsDocType = "Application/pdf"
                };

                var UploadResonce = await _httpClient.PostAsJsonAsync("FileStorage", lmsDmsIndex);

                UploadResonce.EnsureSuccessStatusCode();

                var IndexId = await UploadResonce.Content.ReadAsStringAsync();
                IndexId = IndexId.Substring(1, IndexId.Length - 2);

                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                var DownloadResonce = await _httpClient.GetAsync($"FileStorage/{IndexId}");
                DownloadResonce.EnsureSuccessStatusCode();

                //TODO:
                var DownlaodResString = await DownloadResonce.Content.ReadAsStringAsync();
                var Ob = JsonConvert.DeserializeObject<LmsDmsIndex>(DownlaodResString);


                var lmsDmsIndexPdf = new LmsDmsIndex
                {
                    DmsDocUcode = new Guid().ToString(),
                    DmsDocSourceId = 1,
                    DmsDocSourceTypeId = 1,
                    DmsDocVno = 1,
                    LmsEnbEnbId = 1,
                    LmsEnbEnbSecId = 1,
                    FileData = PdfFileData
                };

                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                var UploadResonce2 = await _httpClient.PutAsJsonAsync("FileStorage/{IndexId}", lmsDmsIndexPdf);

                UploadResonce2.EnsureSuccessStatusCode();

                var IndexId2 = await UploadResonce.Content.ReadAsStringAsync();
                IndexId2 = IndexId.Substring(1, IndexId.Length - 2);

                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                var DownloadResonce2 = await _httpClient.GetAsync($"FileStorage/{IndexId}");
                DownloadResonce2.EnsureSuccessStatusCode();
                Assert.Equal(FileData, Ob.FileData);

                //TODO:
                var DownlaodResString2 = await DownloadResonce2.Content.ReadAsStringAsync();
                var Ob2 = JsonConvert.DeserializeObject<LmsDmsIndex>(DownlaodResString2);


                Assert.Equal(PdfFileData, Ob2.FileData);
            }
        }

        [Fact]
        public async Task FileStreamStorage_Post_ReturnsOkResult()
        {
            var user = new
            {
                UserName = "<EMAIL>",
                Password = "P@ssword"
            };

            // Act
            var response = await _httpClient.PostAsJsonAsync("security/v1/getToken", user);

            // Assert
            response.EnsureSuccessStatusCode();
            var token = await response.Content.ReadAsStringAsync();

            if (token != "")
            {
                token = token.Substring(1, token.Length - 2);
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                var lmsDmsIndex = new LmsDmsIndex
                {
                    DmsDocUcode = new Guid().ToString(),
                    DmsDocSourceId = 1,
                    DmsDocSourceTypeId = 1,
                    DmsDocVno = 1,
                    LmsEnbEnbId = 1,
                    LmsEnbEnbSecId = 1,
                    //FileData = FileData
                };

                var multiForm = new MultipartFormDataContent();

                //// add API method parameters
                //multiForm.Add(new StringContent(token), "token");
                //multiForm.Add(new StringContent(channels), "channels");
                var path = @"C:\DocFiles\This is for verification.docx";
                // add file and directly upload it
                FileStream fs = File.OpenRead(path);

                multiForm.Add(new StreamContent(fs), "file", Path.GetFileName(path));

                HttpResponseMessage UploadResonce;
                try
                {
                     UploadResonce = await _httpClient.PostAsJsonAsync($"v2/FileStorage?DmsDocSourceId={lmsDmsIndex.DmsDocSourceId}&DmsDocSourceTypeId={lmsDmsIndex.DmsDocSourceTypeId}&DmsDocVno={lmsDmsIndex.DmsDocVno}&LmsEnbEnbId={lmsDmsIndex.LmsEnbEnbId}&LmsEnbEnbSecId={lmsDmsIndex.LmsEnbEnbSecId}", multiForm);

                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                    throw;
                }

                UploadResonce.EnsureSuccessStatusCode();

                var IndexId = await UploadResonce.Content.ReadAsStringAsync();
                IndexId = IndexId.Substring(1, IndexId.Length - 2);

                var DownloadResonce = await _httpClient.GetAsync($"FileStorage/{IndexId}");
                DownloadResonce.EnsureSuccessStatusCode();

                //TODO:
                var DownlaodResString = await DownloadResonce.Content.ReadAsStringAsync();
                var Ob = JsonConvert.DeserializeObject<LmsDmsIndex>(DownlaodResString);
                var FileData = await File.ReadAllBytesAsync(@"C:\DocFiles\This is for verification.docx");

                Assert.Equal(FileData, Ob.FileData);
            }
        }

    }
}
