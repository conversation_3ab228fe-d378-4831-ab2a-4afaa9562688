﻿using System.ComponentModel.DataAnnotations;

namespace CaliberFileSystem.Models
{
    public class ASSRIQMigrateFilesRequest
    {
        /// <summary>
        /// The server instance where the databases are hosted.
        /// </summary>
        [Required]
        public string ServerInstance { get; set; }

        /// <summary>
        /// The connection string for the AssureIQ Files database.
        /// </summary>
        [Required]
        public string Assure_IQ_Files { get; set; }

        /// <summary>
        /// The connection string for the AssureIQ Global database.
        /// </summary>
        [Required]
        public string Assure_IQ_Global { get; set; }

        /// <summary>
        /// Flag if pdf flatting is required or not.
        /// </summary>
        [Required]
        public bool pdfFlattning { get; set; } = false;

        /// <summary>
        /// Flag if pdf compression is required or not.
        /// </summary>
        [Required]
        public bool compressPdf { get; set; } = false;

    }

    public class LrnIqMigrateFilesRequest
    {
        /// <summary>
        /// The server instance where the databases are hosted.
        /// </summary>
        [Required]
        public string ServerInstance { get; set; }

        /// <summary>
        /// The connection string for the AssureIQ Files database.
        /// </summary>
        [Required]
        public string Learn_Iq { get; set; }

        /// <summary>
        /// The connection string for the AssureIQ Global database.
        /// </summary>
        [Required]
        public string Lear_Iq_DMS { get; set; }

        /// <summary>
        /// Flag if pdf flatting is required or not.
        /// </summary>
        [Required]
        public bool pdfFlattning { get; set; } = false;

        /// <summary>
        /// Flag if pdf compression is required or not.
        /// </summary>
        [Required]
        public bool compressPdf { get; set; } = false;
    }
    public class LIMSMigrateFilesRequest
    {
        /// <summary>
        /// The server instance where the databases are hosted.
        /// </summary>
        [Required]
        public string ServerInstance { get; set; }

        /// <summary>
        /// The connection string for the CaliberLIMS DMS database.
        /// </summary>
        [Required]
        public string DMS_Db { get; set; }

        /// <summary>
        /// Flag if pdf flatting is required or not.
        /// </summary>
        [Required]
        public bool pdfFlattning { get; set; } = false;

        /// <summary>
        /// Flag if pdf compression is required or not.
        /// </summary>
        [Required]
        public bool compressPdf { get; set; } = false;

        public string SqlUser { get; set; }


        public string SqlPassword { get; set; }

    }


}
