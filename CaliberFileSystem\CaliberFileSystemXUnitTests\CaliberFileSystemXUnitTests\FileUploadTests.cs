﻿using Newtonsoft.Json;
using System.Net.Http.Headers;
using System.Net.Http.Json;

namespace CaliberFileSystemXUnitTests
{
    /// <summary>
    /// Unit tests for file upload functionality in the CaliberFileStorage system.
    /// </summary>
    public class FileUploadTests
    {
        private readonly HttpClient _httpClient;

        /// <summary>
        /// Initializes a new instance of the <see cref="FileUploadTests"/> class and sets up the HttpClient with the base address.
        /// </summary>
        public FileUploadTests()
        {
            // Initialize the HttpClient with the base address of your application
            _httpClient = new HttpClient
            {
                BaseAddress = new Uri("http://localhost/CaliberFileStorage/") // Replace with your application's base address
            };
        }

        /// <summary>
        /// Tests the file upload functionality to ensure it returns an OK result.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        [Fact]
        public async Task UploadFile_ReturnsOkResult()
        {
            // Arrange
            var tokenResponse = await GetAuthToken();
            var token = await tokenResponse.Content.ReadAsStringAsync();

            // Remove the quotes around the token
            token = token.Substring(1, token.Length - 2);

            // Assert that the token is not empty
            Assert.False(string.IsNullOrEmpty(token), "Authentication token should not be empty.");

            // Set the authorization header with the Bearer token
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
   
            // File to be uploaded
            var filePath = @"C:\Temp\FileStorage\sample.pdf";
            using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);

            var fileContent = new StreamContent(fileStream);
            fileContent.Headers.ContentType = new MediaTypeHeaderValue("application/pdf");

            // Create form data to simulate file upload
            var formData = new MultipartFormDataContent();
            formData.Add(fileContent, "file", Path.GetFileName(filePath));

            // Act
            var response = await _httpClient.PostAsync("/CaliberFileStorage/UploadFile", formData);

            // Assert
            response.EnsureSuccessStatusCode();
            var responseContent = await response.Content.ReadAsStringAsync();

            // Deserialize the response to extract the file upload key
            var uploadResult = JsonConvert.DeserializeObject<Dictionary<string, dynamic>>(responseContent);
            string key = uploadResult["key"];

            // Ensure the key is not empty
            Assert.False(string.IsNullOrEmpty(key), "File upload key should not be empty.");
        }

        /// <summary>
        /// Retrieves an authentication token by sending a POST request with user credentials.
        /// </summary>
        /// <returns>An HttpResponseMessage containing the authentication token.</returns>
        private async Task<HttpResponseMessage> GetAuthToken()
        {
            var user = new
            {
                UserName = "<EMAIL>",
                Password = "P@ssword"
            };

            // Send a POST request to get the authentication token
            return await _httpClient.PostAsJsonAsync("security/v1/getToken", user);
        }
    }
}
