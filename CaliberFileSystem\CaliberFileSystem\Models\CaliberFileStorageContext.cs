﻿using Microsoft.EntityFrameworkCore;

namespace CaliberFileSystem.Models;

public partial class CaliberFileStorageContext : DbContext
{
    public CaliberFileStorageContext()
    {
    }

    public CaliberFileStorageContext(DbContextOptions<CaliberFileStorageContext> options)
        : base(options)
    {
    }

    public virtual DbSet<LmsDmsIndex> LmsDmsIndices { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        => optionsBuilder.UseSqlServer("name=DBCon");

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<LmsDmsIndex>(entity =>
        {
            entity.HasKey(e => e.LmsDmsIndexId).HasName("PK__LMS_DMS___380A20D1902CB37D");

            entity.ToTable("LMS_DMS_INDEX");

            entity.Property(e => e.LmsDmsIndexId)
                .HasMaxLength(50)
                .HasDefaultValueSql("('')")
                .HasColumnName("LMS_DMS_INDEX_ID");
            entity.Property(e => e.AdPlantId).HasColumnName("AD_PLANT_ID");
            entity.Property(e => e.DmsDocDesc)
                .HasMaxLength(500)
                .HasDefaultValueSql("('')")
                .HasColumnName("DMS_DOC_DESC");
            entity.Property(e => e.DmsDocFileExt)
                .HasMaxLength(50)
                .HasDefaultValueSql("('')")
                .HasColumnName("DMS_DOC_FILE_EXT");
            entity.Property(e => e.DmsDocInitBy).HasColumnName("DMS_DOC_INIT_BY");
            entity.Property(e => e.DmsDocInitEsign)
                .HasMaxLength(100)
                .HasDefaultValueSql("('')")
                .HasColumnName("DMS_DOC_INIT_ESIGN");
            entity.Property(e => e.DmsDocInitOn)
                .HasDefaultValueSql("('')")
                .HasColumnName("DMS_DOC_INIT_ON");
            entity.Property(e => e.DmsDocKeyWords)
                .HasMaxLength(500)
                .HasDefaultValueSql("('')")
                .HasColumnName("DMS_DOC_KEY_WORDS");
            entity.Property(e => e.DmsDocSourceId).HasColumnName("DMS_DOC_SOURCE_ID");
            entity.Property(e => e.DmsDocSourceTypeId)
                .HasDefaultValueSql("((1))")
                .HasColumnName("DMS_DOC_SOURCE_TYPE_ID");
            entity.Property(e => e.DmsDocStatus).HasColumnName("DMS_DOC_STATUS");
            entity.Property(e => e.DmsDocType)
                .HasMaxLength(500)
                .HasDefaultValueSql("('')")
                .HasColumnName("DMS_DOC_TYPE");
            entity.Property(e => e.DmsDocUcode)
                .HasMaxLength(50)
                .HasDefaultValueSql("('')")
                .HasColumnName("DMS_DOC_UCODE");
            entity.Property(e => e.DmsDocVno)
                .HasDefaultValueSql("((1))")
                .HasColumnName("DMS_DOC_VNO");
            entity.Property(e => e.LmsDemTabName)
                .HasMaxLength(25)
                .HasDefaultValueSql("('')")
                .HasColumnName("LMS_DEM_TAB_NAME");
            entity.Property(e => e.LmsDmsDocRepoTpeId).HasColumnName("LMS_DMS_DOC_REPO_TPE_ID");
            entity.Property(e => e.LmsEnbEnbCode).HasColumnName("LMS_ENB_ENB_CODE");
            entity.Property(e => e.LmsEnbEnbId).HasColumnName("LMS_ENB_ENB_ID");
            entity.Property(e => e.LmsEnbEnbSecId).HasColumnName("LMS_ENB_ENB_SEC_ID");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
