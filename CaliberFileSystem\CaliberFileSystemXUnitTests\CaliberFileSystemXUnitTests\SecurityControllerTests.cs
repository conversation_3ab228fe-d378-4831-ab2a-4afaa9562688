﻿using System.Net.Http.Json;
using System.Net;

namespace CaliberFileSystemXUnitTests
{
    public class SecurityControllerTests
    {
        private readonly HttpClient _httpClient;

        public SecurityControllerTests()
        {
            // Initialize the HttpClient with the base address of your application
            _httpClient = new HttpClient
            {
                BaseAddress = new Uri("http://localhost:32769") // Replace with your application's base address
            };
        }

        [Fact]
        public async Task GetToken_ValidCredentials_ReturnsToken()
        {
            // Arrange
            var user = new
            {
                UserName = "<EMAIL>",
                Password = "P@ssword"
            };

            // Act
            var response = await _httpClient.PostAsJsonAsync("/security/v1/getToken", user);

            // Assert
            response.EnsureSuccessStatusCode();
            var token = await response.Content.ReadAsStringAsync();
            Assert.NotNull(token);
            // Add more assertions as needed
        }

        [Fact]
        public async Task GetToken_InvalidCredentials_ReturnsUnauthorized()
        {
            // Arrange
            var user = new
            {
                UserName = "invalidUser",
                Password = "invalidPassword"
            };

            // Act
            var response = await _httpClient.PostAsJsonAsync("/security/v1/getToken", user);

            // Assert
            Assert.Equal(HttpStatusCode.Unauthorized, response.StatusCode);
        }

        // Add more test methods to cover other scenarios

        public void Dispose()
        {
            // Dispose the HttpClient after all tests have finished
            _httpClient.Dispose();
        }
    }
}
