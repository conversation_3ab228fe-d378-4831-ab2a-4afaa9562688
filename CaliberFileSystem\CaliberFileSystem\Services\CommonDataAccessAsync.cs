using System.Data;
using System.Data.SqlClient;
using System.Dynamic;
using Caliber.Application;
using Dapper;
namespace Caliber.DataAccess
{
    public class CommonDataAccessAsync : ICommonDataAccessAsync //, ICommonDataAccess
    {
        public CommonDataAccessAsync(string ConStr, int? commandTimeout = null)
        {
            MyConStr = ConStr;
            CommandTimeout = commandTimeout;
        }
        private int? CommandTimeout { get; set; }
        private string MyConStr { get; set; }
        public async Task WriteDataAsync(CommandType commandType, string commandText, object inputParams)
        {
            await ExtWriteDataAsync(MyConStr, commandType, commandText, inputParams, null);
        }
        public async Task<object> WriteDataAsync(CommandType commandType, string commandText, object inputParams, object outputParams)
        {
            return await ExtWriteDataAsync(MyConStr, commandType, commandText, inputParams, null, outputParams);
        }
        public async Task<object> WriteDataAsync(CommandType commandType, string commandText, List<object> inputParamsList, object outputParams)
        {
            return await ExtWriteDataAsync(MyConStr, commandType, commandText, inputParamsList, null, outputParams);
        }
        public async Task<object> WriteDataAsync(CommandType commandType, string commandText, List<object> inputParamsList, Dictionary<string, object> runtimeParams, object outputParams)
        {
            return await ExtWriteDataAsync(MyConStr, commandType, commandText, inputParamsList, runtimeParams, outputParams);
        }
        public async Task<object> WriteDataAsync(CommandType commandType, string commandText, object inputParamsList, Dictionary<string, object> runtimeParams, object outputParams)
        {
            return await ExtWriteDataAsync(MyConStr, commandType, commandText, inputParamsList, runtimeParams, outputParams);
        }
        public async Task<T> ReadDataAsync<T>(CommandType commandType, string commandText, object param)
        {
            try
            {
                await using var connection = new SqlConnection(MyConStr);
                return (await connection.QueryAsync<T>(commandText, param, commandType: commandType, commandTimeout: CommandTimeout)).FirstOrDefault();
            }
            catch (Exception ex)
            {
                List<object> inputParamList = new() { param };
                ex = await ExceptionDataLogAsync(inputParamList, null, commandText, MyConStr, ex);
                throw ex;
            }

            return default;
        }
        public async Task<(T, TQ, List<TU>)> ReadDataAsync<T, TQ, TU>(CommandType commandType, string commandText, object param)
        {
            try
            {
                await using var connection = new SqlConnection(MyConStr);

                var results = await connection.QueryMultipleAsync(commandText, param, commandType: commandType, commandTimeout: CommandTimeout);
                var dataCollectionT = (await results.ReadAsync<T>()).FirstOrDefault();
                var dataCollectionQ = (await results.ReadAsync<TQ>()).FirstOrDefault();
                var dataCollectionU = (await results.ReadAsync<TU>()).ToList();
                return (dataCollectionT, dataCollectionQ, dataCollectionU);
            }
            catch (Exception ex)
            {
                List<object> inputParamList = new() { param };
                ex = await ExceptionDataLogAsync(inputParamList, null, commandText, MyConStr, ex);
                throw ex;
            }
        }
        public async Task<List<T>> ReadDataListAsync<T>(CommandType commandType, string commandText, object param)
        {
            try
            {
                await using var connection = new SqlConnection(MyConStr);

                return (await connection.QueryAsync<T>(commandText, param, commandType: commandType, commandTimeout: CommandTimeout))?.ToList();
            }
            catch (Exception ex)
            {
                List<object> inputParamList = new() { param };
                ex = await ExceptionDataLogAsync(inputParamList, null, commandText, MyConStr, ex);
                throw ex;
            }
        }
        public async Task<List<T>> ReadDataListAsync<T>(CommandType commandType, string commandText, object param, Dictionary<string, object> RuntimeParams)
        {
            try
            {
                var @params = new DynamicParameters();
                @params.AddDynamicParams(param);
                if (RuntimeParams != null)
                {
                    foreach (var (key, value) in RuntimeParams)
                    {
                        @params.Add(key, value);
                    }
                }

                await using var connection = new SqlConnection(MyConStr);

                return (await connection.QueryAsync<T>(commandText, @params, commandType: commandType, commandTimeout: CommandTimeout))?.ToList();
            }
            catch (Exception ex)
            {
                List<object> inputParamList = new() { param };
                ex = await ExceptionDataLogAsync(inputParamList, null, commandText, MyConStr, ex);
                throw ex;
            }
        }
        public async Task<List<T>> ReadDataListAsync<T>(CommandType commandType, string commandText, List<object> param)
        {
            try
            {
                var @params = new DynamicParameters();
                foreach (var inputParams in param)
                {
                    @params.AddDynamicParams(inputParams);
                }
                await using var connection = new SqlConnection(MyConStr);
                return (await connection.QueryAsync<T>(commandText, @params, commandType: commandType, commandTimeout: CommandTimeout))?.ToList();
            }
            catch (Exception ex)
            {
                List<object> inputParamList = new() { param };
                ex = await ExceptionDataLogAsync(inputParamList, null, commandText, MyConStr, ex);
                throw ex;
            }
        }
        public async Task<(List<T>, int)> ReadDataListAsync<T>(CommandType commandType, string commandText, object param, int pageNo, int pageSize)
        {
            try
            {
                await using var connection = new SqlConnection(MyConStr);

                var dataCollectionT = (await connection.QueryAsync<T>(commandText, param, commandType: commandType, commandTimeout: CommandTimeout))?.ToList();

                if (dataCollectionT == null)
                    return default;

                var maxRecordCount = dataCollectionT.Count;
                if (dataCollectionT.Count <= 0) return (dataCollectionT, 0);
                var startIndex = pageSize * (pageNo - 1) - 1;
                var endIndex = pageSize * pageNo;
                return (dataCollectionT.Where((_, index) => index > startIndex && index < endIndex).ToList(), maxRecordCount);
            }
            catch (Exception ex)
            {
                List<object> inputParamList = new() { param };
                ex = await ExceptionDataLogAsync(inputParamList, null, commandText, MyConStr, ex);
                throw ex;
            }
        }
        public async Task<(List<T>, int)> ReadDataListAsync<T>(CommandType commandType, string commandText, List<object> param, int pageNo, int pageSize)
        {
            try
            {
                var @params = new DynamicParameters();
                foreach (var inputParams in param)
                {
                    @params.AddDynamicParams(inputParams);
                }

                await using var connection = new SqlConnection(MyConStr);

                var dataCollectionT = (await connection.QueryAsync<T>(commandText, @params, commandType: commandType, commandTimeout: CommandTimeout))?.ToList();

                if (dataCollectionT == null)
                    return default;

                var maxRecordCount = dataCollectionT.Count;
                if (dataCollectionT.Count <= 0) return (dataCollectionT, 0);
                var startIndex = pageSize * (pageNo - 1) - 1;
                var endIndex = pageSize * pageNo;
                return (dataCollectionT.Where((_, index) => index > startIndex && index < endIndex).ToList(), maxRecordCount);
            }
            catch (Exception ex)
            {
                List<object> inputParamList = new() { param };
                ex = await ExceptionDataLogAsync(inputParamList, null, commandText, MyConStr, ex);
                throw ex;
            }
        }
        public async Task<(List<T>, List<TQ>)> ReadDataListAsync<T, TQ>(CommandType commandType, string commandText, object param)
        {
            try
            {
                await using var connection = new SqlConnection(MyConStr);

                var results = await connection.QueryMultipleAsync(commandText, param, commandType: commandType, commandTimeout: CommandTimeout);
                if (results == null)
                    return default;
                var dataCollectionT = (await results.ReadAsync<T>()).ToList();
                var dataCollectionQ = (await results.ReadAsync<TQ>()).ToList();
                return (dataCollectionT, dataCollectionQ);
            }
            catch (Exception ex)
            {
                List<object> inputParamList = new() { param };
                ex = await ExceptionDataLogAsync(inputParamList, null, commandText, MyConStr, ex);
                throw ex;
            }
        }
        public async Task<(List<T>, List<TQ>, List<TU>)> ReadDataListAsync<T, TQ, TU>(CommandType commandType, string commandText, object param)
        {
            try
            {
                await using var connection = new SqlConnection(MyConStr);

                var results = await connection.QueryMultipleAsync(commandText, param, commandType: commandType, commandTimeout: CommandTimeout);
                if (results == null)
                    return default;
                var dataCollectionT = (await results.ReadAsync<T>())?.ToList();
                var dataCollectionQ = (await results.ReadAsync<TQ>())?.ToList();
                var dataCollectionU = (await results.ReadAsync<TU>())?.ToList();
                return (dataCollectionT, dataCollectionQ, dataCollectionU);
            }
            catch (Exception ex)
            {
                List<object> inputParamList = new() { param };
                ex = await ExceptionDataLogAsync(inputParamList, null, commandText, MyConStr, ex);
                throw ex;
            }
        }
        public async Task ExtWriteDataAsync(string conStr, CommandType commandType, string commandText, object inputParams, Dictionary<string, object> runtimeParams)
        {
            object outputParams = new { };
            await ExtWriteDataAsync(conStr, commandType, commandText, inputParams, runtimeParams, outputParams);
        }
        public async Task<object> ExtWriteDataAsync(string conStr, CommandType commandType, string commandText, object inputParams, Dictionary<string, object> runtimeParams, object outputParams)
        {
            var objList = new List<object> { inputParams };
            return await ExtWriteDataAsync(conStr, commandType, commandText, objList, runtimeParams, outputParams);
        }
        public async Task<object> ExtWriteDataAsync(string conStr, CommandType commandType, string commandText, List<object> inputParamsList, Dictionary<string, object> RuntimeParams, object outputParams)
        {
            var @params = new DynamicParameters();
            foreach (var inputParams in inputParamsList)
            {
                @params.AddDynamicParams(inputParams);
            }
            if (RuntimeParams != null)
            {
                foreach (var (key, value) in RuntimeParams)
                {
                    @params.Add(key, value);
                }
            }
            if (outputParams != null)
            {
                foreach (var prop in outputParams.GetType().GetProperties())
                {
                    @params.Add(prop.Name, prop.GetValue(outputParams, null), direction: ParameterDirection.Output);
                }
            }
            await using (var connection = new SqlConnection(conStr))
            {
                connection.Open();
                await using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        await connection.ExecuteAsync(commandText, @params, transaction, commandType: commandType, commandTimeout: CommandTimeout);
                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        ex = await ExceptionDataLogAsync(inputParamsList, outputParams, commandText, conStr, ex);
                        throw;
                    }
                }
            }
            if (outputParams != null)
            {
                foreach (var prop in outputParams.GetType().GetProperties())
                {
                    switch (true)
                    {
                        case { } when prop.PropertyType == typeof(int):
                            prop.SetValue(outputParams, @params.Get<int?>(prop.Name), null);
                            break;
                        case { } when prop.PropertyType == typeof(double):
                            prop.SetValue(outputParams, @params.Get<double>(prop.Name), null);
                            break;
                        case { } when prop.PropertyType == typeof(double?):
                            prop.SetValue(outputParams, @params.Get<double>(prop.Name), null);
                            break;
                        case { } when prop.PropertyType == typeof(string):
                            prop.SetValue(outputParams, @params.Get<string>(prop.Name));
                            break;
                        case { } when prop.PropertyType == typeof(DateTime?):
                            prop.SetValue(outputParams, @params.Get<DateTime>(prop.Name), null);
                            break;
                        case { } when prop.PropertyType == typeof(DateTime):
                            prop.SetValue(outputParams, @params.Get<DateTime>(prop.Name), null);
                            break;
                    }
                }
            }

            return outputParams;
        }
        public async Task<IEnumerable<dynamic>> ReadAsIEnumerableAsync(CommandType commandType, string commandText, object param)
        {
            await using var connection = new SqlConnection(MyConStr);

            return await connection.QueryAsync<dynamic>(sql: commandText, param: param,
                    commandType: commandType, commandTimeout: CommandTimeout);
        }
        public async Task<IEnumerable<ExpandoObject>> ReadAsIEnumerableAsync(CommandType commandType, string commandText)
        {
            try
            {
                await using var connection = new SqlConnection(MyConStr);

                return (await connection.QueryAsync(commandText, null, commandType: commandType, commandTimeout: CommandTimeout)).Select(x => (ExpandoObject)ToExpandoObject(x));
            }
            catch (Exception ex)
            {
                ex = await ExceptionDataLogAsync(null, null, commandText, MyConStr, ex);
                throw;
            }
        }
        public async Task<IEnumerable<ExpandoObject>> ReadDataAsIEnumerableAsync(CommandType commandType, string commandText, object param, Dictionary<string, object> RuntimeParams)
        {
            try
            {
                var @params = new DynamicParameters();
                //@params.AddDynamicParams(param);
                if (param != null)
                {
                    foreach (var prop in param.GetType().GetProperties())
                    {
                        @params.Add(prop.Name, prop.GetValue(param, null), direction: ParameterDirection.Input);
                    }
                }
                if (RuntimeParams != null)
                {
                    foreach (var (key, value) in RuntimeParams)
                    {
                        @params.Add(key, value.ToString());
                    }
                }
                await using var connection = new SqlConnection(MyConStr);

                return (await connection.QueryAsync(commandText, @params, commandType: commandType, commandTimeout: CommandTimeout)).Select(x => (ExpandoObject)ToExpandoObject(x));
            }
            catch (Exception ex)
            {
                List<object> inputParamList = new() { param };
                ex = await ExceptionDataLogAsync(inputParamList, null, commandText, MyConStr, ex);
                throw;
            }
        }
        public async Task<Exception> ExceptionDataLogAsync(List<object> inputParamsList, object outputParams, string commandText, string conStr, Exception ex)
        {
            ex.Data.Add("inputParamsList", inputParamsList);
            ex.Data.Add("outputParams", outputParams);
            ex.Data.Add("commandText", commandText);
            await using var connection = new SqlConnection(conStr);
            ex.Data.Add("DBName", connection.Database);

            return ex;
        }
        public void Dispose()
        {
        }
        public dynamic ToExpandoObject(object value)
        {
            var dapperRowProperties = value as IDictionary<string, object>;
            IDictionary<string, object> expando = new ExpandoObject();
            if (dapperRowProperties == null)
            {
                return (ExpandoObject)expando;
            }

            foreach (var property in dapperRowProperties)
                expando.Add(property.Key, property.Value);

            return (ExpandoObject)expando;
        }

    }

    public interface IGlobalDataAccessAsync : ICommonDataAccessAsync { }
    public class GlobalDataAccessAsync : CommonDataAccessAsync, IGlobalDataAccessAsync
    {
        public GlobalDataAccessAsync() : base("GCon")
        {

        }
    }
}
