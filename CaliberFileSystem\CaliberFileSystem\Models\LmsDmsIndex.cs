﻿using System.ComponentModel.DataAnnotations.Schema;

namespace CaliberFileSystem.Models;

public partial class LmsDmsIndex
{
    public string LmsDmsIndexId { get; set; } = null!;

    public string DmsDocUcode { get; set; } = null!;

    public string DmsDocDesc { get; set; } = null!;

    public int LmsEnbEnbId { get; set; }

    public int LmsEnbEnbCode { get; set; }

    public int LmsEnbEnbSecId { get; set; }

    public int LmsDmsDocRepoTpeId { get; set; }

    public string DmsDocKeyWords { get; set; } = null!;

    public int DmsDocVno { get; set; }

    public int DmsDocStatus { get; set; }

    public int DmsDocInitBy { get; set; }

    public DateTime DmsDocInitOn { get; set; }

    public string DmsDocInitEsign { get; set; } = null!;

    public int DmsDocSourceTypeId { get; set; }

    public int DmsDocSourceId { get; set; }

    public string LmsDemTabName { get; set; } = null!;

    public byte AdPlantId { get; set; }

    public string DmsDocFileExt { get; set; } = null!;

    public string DmsDocType { get; set; } = null!;
    [NotMapped]
    public byte[] FileData { get; set; }
}
