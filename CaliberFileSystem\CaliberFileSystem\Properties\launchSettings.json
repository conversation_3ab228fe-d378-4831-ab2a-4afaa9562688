{"profiles": {"Docker": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/swagger", "environmentVariables": {"ASPNETCORE_HTTP_PORTS": "8080"}, "publishAllPorts": true}, "Profile 1": {"commandName": "IIS", "launchUrl": "swagger"}}, "$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iis": {"applicationUrl": "http://localhost/CaliberFileStorage", "sslPort": 0}, "iisExpress": {"applicationUrl": "http://localhost:40403", "sslPort": 0}}}