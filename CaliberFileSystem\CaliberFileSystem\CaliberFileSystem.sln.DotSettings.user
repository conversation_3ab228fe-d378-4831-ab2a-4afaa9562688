﻿<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=2cc6092d_002D499c_002D45af_002D83f4_002D3520fdf8590d/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" IsActive="True" Name="FileStorage_Post_ReturnsOkResult" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;TestAncestor&gt;&#xD;
    &lt;TestId&gt;xUnit::ABA5DD94-C26A-4990-BB99-F3E412537188::net8.0::CaliberFileSystemXUnitTests.FileStorageControllerTests&lt;/TestId&gt;&#xD;
  &lt;/TestAncestor&gt;&#xD;
&lt;/SessionState&gt;</s:String></wpf:ResourceDictionary>