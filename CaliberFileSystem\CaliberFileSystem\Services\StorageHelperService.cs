﻿using Amazon.S3.Model;
using Azure.Core;
using Azure.Identity;
using BlobHelper;
using Caliber.Application;
using CaliberFileSystem.Models;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using System.Net.Http.Headers;

namespace CaliberFileSystem.Services
{
    /// <summary>
    /// Interface defining the operations for storage management services.
    /// </summary>
    public interface IStorageHelperService
    {
        Task<string> Write(string contentType, byte[] data);
        Task<string> WriteStream(string contentType, IFormFile fileStream);
        Task<string> WriteExtended(string key, string contentType, byte[] data);
        Task<string> Update(string key, string contentType, byte[] data);
        Task<string> WriteAndGenerateUrl(string contentType, byte[] data);
        Task<byte[]> Read(string key);
        Task<byte[]> ReadFileBytes(string key);
        Task<Stream> ReadFileStream(string key);
    }

    /// <summary>
    /// Service for handling file storage operations.
    /// Supports different storage types, including blob storage and database storage.
    /// </summary>
    public class StorageHelperService : IStorageHelperService
    {
        // Blob client to interact with blob storage
        private BlobClient _Blobs { get; set; }

        // Specifies the type of file storage (Database or Blob)
        private FileStorageType _StorageType { get; set; }

        // Common data access service for database operations
        private ICommonDataAccessAsync CommonDataAccessAsync { get; }

        private readonly IConfiguration _configuration;

        /// <summary>
        /// Constructor to initialize the storage service with dependencies.
        /// </summary>
        /// <param name="blobs">Blob client for storage operations.</param>
        /// <param name="storageType">Type of storage to use (Database or Blob).</param>
        /// <param name="commonDataAccessAsync">Data access service for database operations.</param>
        public StorageHelperService(BlobClient blobs, FileStorageType storageType, ICommonDataAccessAsync commonDataAccessAsync, IConfiguration configuration)
        {
            _Blobs = blobs;
            _StorageType = storageType;
            CommonDataAccessAsync = commonDataAccessAsync;
            _configuration = configuration;
        }

        /// <summary>
        /// Writes a file stream to the storage.
        /// </summary>
        /// <param name="contentType">MIME type of the file.</param>
        /// <param name="file">File stream to write.</param>
        /// <returns>Key of the stored file.</returns>
        public async Task<string> WriteStream(string contentType, IFormFile file)
        {
            var fileStream = file.OpenReadStream(); // Open the file stream
            long streamLength = -1; // Default value if length is not available

            // Check if the stream supports seeking to determine the length
            if (fileStream.CanSeek)
            {
                streamLength = fileStream.Length;
            }

            string key = Guid.NewGuid().ToString(); // Generate a unique key
            await _Blobs.Write(key, contentType, streamLength, fileStream); // Write the stream to blob storage

            return key; // Return the generated key
        }

        /// <summary>
        /// Reads a file stream from storage.
        /// </summary>
        /// <param name="key">Key to identify the file.</param>
        /// <returns>Stream of the file data.</returns>
        public async Task<Stream> ReadFileStream(string key)
        {
            Stream stream = null;
            try
            {
                var FileData = await _Blobs.GetStream(key); // Retrieve the file stream from storage
                stream = FileData.Data; // Assign the retrieved stream
            }
            catch
            {
                // Handle exception if needed (e.g., logging)
            }
            return stream; // Return the stream (or null if an error occurred)
        }

        /// <summary>
        /// Writes byte data to the storage with a specified content type.
        /// </summary>
        /// <param name="contentType">MIME type of the file.</param>
        /// <param name="data">Byte array of file data.</param>
        /// <returns>Key of the stored file.</returns>
        public async Task<string> Write(string contentType, byte[] data)
        {
            string key = Guid.NewGuid().ToString(); // Generate a unique key

            // Check storage type and write data accordingly
            if (_StorageType == FileStorageType.DataBase)
            {
                // Store file in the database
                object outParam = new OutParam();
                outParam = await CommonDataAccessAsync.WriteDataAsync(
                    System.Data.CommandType.StoredProcedure,
                    "usp_InsertFileIntoFileTable",
                    new { FileName = key, Type = 1, FileData = data },
                    outParam
                );
            }
            else
            {
                // Store file in blob storage
                await _Blobs.Write(key, contentType, data);
            }

            return key; // Return the generated key
        }

        /// <summary>
        /// Writes byte data to storage using a specified key.
        /// </summary>
        /// <param name="key">Key to identify the file.</param>
        /// <param name="contentType">MIME type of the file.</param>
        /// <param name="data">Byte array of file data.</param>
        /// <returns>Key of the stored file.</returns>
        public async Task<string> WriteExtended(string key, string contentType, byte[] data)
        {
            // Check storage type and write data accordingly
            if (_StorageType == FileStorageType.DataBase)
            {
                // Store file in the database
                object outParam = new OutParam();
                outParam = await CommonDataAccessAsync.WriteDataAsync(
                    System.Data.CommandType.StoredProcedure,
                    "usp_InsertFileIntoFileTable",
                    new { FileName = key, Type = 1, FileData = data },
                    outParam
                );
            }
            else if (_StorageType == FileStorageType.Sharepoint)
            {
                // Call separate method for OneDrive upload
                await UploadToSharepoint(key, data);
            }
            else
            {
                // Store file in blob storage
                using (var memoryStream = new MemoryStream(data))
                {
                    await _Blobs.Write(key, contentType, memoryStream.ToArray());
                }
            }

            return key; // Return the key
        }

        /// <summary>
        /// Uploads a file to Sharepoint using the specified key and data.
        /// </summary>
        /// <param name="key">Key to identify the file in OneDrive.</param>
        /// <param name="data">Byte array of file data.</param>
        /// <returns>Task representing the async operation.</returns>
        private async Task UploadToSharepoint(string key, byte[] data)
        {
            // Generate the path and filename for OneDrive
            string[] pathSegments = key.Split(new[] { '\\' }, StringSplitOptions.RemoveEmptyEntries);

            var clientId = _configuration["StorageOptions:SharepointSettings:ClientId"];
            var clientSecret = _configuration["StorageOptions:SharepointSettings:ClientSecret"];
            var tenantId = _configuration["StorageOptions:SharepointSettings:TenantId"];
            string DriveId = _configuration["StorageOptions:SharepointSettings:DriveId"];

            // Authenticate and create Graph client
            var options = new TokenCredentialOptions { AuthorityHost = AzureAuthorityHosts.AzurePublicCloud };
            var clientSecretCredential = new ClientSecretCredential(tenantId, clientId, clientSecret, options);
            var graphClient = new GraphServiceClient(clientSecretCredential, new[] { "https://graph.microsoft.com/.default" });

            // Ensure path exists
            string parentId = "root";
            foreach (var segment in pathSegments.Take(pathSegments.Length - 1))
            {
                // Retrieve the children of the parent item to find or create the folder
                var items = await graphClient.Drives[DriveId].Items[parentId].Children.GetAsync();

                var folder = items.Value.FirstOrDefault(item => item.Name == segment && item.Folder != null);
                if (folder == null)
                {
                    // Folder doesn't exist, create it
                    folder = await graphClient.Drives[DriveId].Items[parentId].Children.PostAsync(new DriveItem
                    {
                        Name = segment,
                        Folder = new Folder() // Set as folder
                    });
                }
                parentId = folder.Id;
            }

            // Upload the file to the final path
            string fileName = pathSegments.Last();
            var uploadUrl = $"https://graph.microsoft.com/v1.0/drives/{DriveId}/items/{parentId}:/{fileName}:/content";

            using (var httpClient = new HttpClient())
            {
                var tokenRequestContext = new TokenRequestContext(new[] { "https://graph.microsoft.com/.default" });
                var accessTokenResult = await clientSecretCredential.GetTokenAsync(tokenRequestContext);
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessTokenResult.Token);

                using (var httpRequest = new HttpRequestMessage(HttpMethod.Put, uploadUrl))
                {
                    httpRequest.Content = new ByteArrayContent(data);
                    httpRequest.Content.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");

                    using (var response = await httpClient.SendAsync(httpRequest))
                    {
                        response.EnsureSuccessStatusCode();
                    }
                }
            }
        }
        /// <summary>
        /// Read a file from Sharepoint using the specified key.
        /// </summary>
        /// <param name="key">Key to identify the file in OneDrive.</param>
        /// <returns>requested file with bytes.</returns>
        private async Task<byte[]> ReadFromSharepoint(string key)
        {
            // Split the key into path segments
            string[] pathSegments = key.Split(new[] { '\\' }, StringSplitOptions.RemoveEmptyEntries);

            var clientId = _configuration["StorageOptions:SharepointSettings:ClientId"];
            var clientSecret = _configuration["StorageOptions:SharepointSettings:ClientSecret"];
            var tenantId = _configuration["StorageOptions:SharepointSettings:TenantId"];
            string DriveId = _configuration["StorageOptions:SharepointSettings:DriveId"];

            // Authenticate and create Graph client
            var options = new TokenCredentialOptions { AuthorityHost = AzureAuthorityHosts.AzurePublicCloud };
            var clientSecretCredential = new ClientSecretCredential(tenantId, clientId, clientSecret, options);
            var graphClient = new GraphServiceClient(clientSecretCredential, new[] { "https://graph.microsoft.com/.default" });

            // Navigate through the folder structure
            string parentId = "root";
            foreach (var segment in pathSegments.Take(pathSegments.Length - 1))
            {
                // Retrieve the children of the parent item to find the folder
                var items = await graphClient.Drives[DriveId].Items[parentId].Children.GetAsync();

                var folder = items.Value.FirstOrDefault(item => item.Name == segment && item.Folder != null);
                if (folder == null)
                {
                    throw new FileNotFoundException($"Folder '{segment}' not found in SharePoint path.");
                }
                parentId = folder.Id;
            }

            // Retrieve the file
            string fileName = pathSegments.Last();
            var itemsInFolder = await graphClient.Drives[DriveId].Items[parentId].Children.GetAsync();
            var file = itemsInFolder.Value.FirstOrDefault(item => item.Name == fileName && item.File != null);

            if (file == null)
            {
                throw new FileNotFoundException($"File '{fileName}' not found in SharePoint.");
            }

            // Download the file content
            using (var stream = await graphClient.Drives[DriveId].Items[file.Id].Content.GetAsync())
            {
                using (var memoryStream = new MemoryStream())
                {
                    await stream.CopyToAsync(memoryStream);
                    return memoryStream.ToArray();
                }
            }
        }

        /// <summary>
        /// Updates existing file data in storage with a specified key.
        /// </summary>
        /// <param name="key">Key of the file to update.</param>
        /// <param name="contentType">MIME type of the file.</param>
        /// <param name="data">Byte array of file data.</param>
        /// <returns>Key of the updated file.</returns>
        public async Task<string> Update(string key, string contentType, byte[] data)
        {
            // Check storage type and update data accordingly
            if (_StorageType == FileStorageType.DataBase)
            {
                // Update file in the database
                object outParam = new OutParam();
                outParam = await CommonDataAccessAsync.WriteDataAsync(
                    System.Data.CommandType.StoredProcedure,
                    "usp_InsertFileIntoFileTable",
                    new { FileName = key, Type = 2, FileData = data },
                    outParam
                );
            }
            else
            {
                // Update file in blob storage
                await _Blobs.Write(key, contentType, data);
            }

            return key; // Return the key
        }

        /// <summary>
        /// Writes data to storage and generates a URL for the stored file.
        /// </summary>
        /// <param name="contentType">MIME type of the file.</param>
        /// <param name="data">Byte array of file data.</param>
        /// <returns>URL of the stored file.</returns>
        public async Task<string> WriteAndGenerateUrl(string contentType, byte[] data)
        {
            string key = Guid.NewGuid().ToString(); // Generate a unique key

            // Check storage type and write data accordingly
            if (_StorageType == FileStorageType.DataBase)
            {
                return null; // Return null for database storage (URL not applicable)
            }
            else
            {
                // Store file in blob storage
                await _Blobs.Write(key, contentType, data);
                string url = _Blobs.GenerateUrl(key); // Generate URL for the stored file
                return url; // Return the URL
            }
        }

        /// <summary>
        /// Reads byte data from storage using a specified key.
        /// </summary>
        /// <param name="key">Key to identify the file.</param>
        /// <returns>Byte array of file data.</returns>
        public async Task<byte[]> Read(string key)
        {
            // Check storage type and read data accordingly
            if (_StorageType == FileStorageType.DataBase)
            {
                // Read file from the database
                var FileData = await CommonDataAccessAsync.ReadDataAsync<FileData>(
                    System.Data.CommandType.Text,
                    "select file_stream file_stream from FILEDATA where stream_id = @FileName",
                    new { FileName = key }
                );
                return FileData.file_stream; // Return the file stream
            }
            else
            {
                // Read file from blob storage
                byte[] data = await _Blobs.Get(key);
                return data; // Return the file data
            }
        }

        /// <summary>
        /// Reads file bytes from storage using a specified key.
        /// </summary>
        /// <param name="key">Key to identify the file.</param>
        /// <returns>Byte array of file data.</returns>
        public async Task<byte[]> ReadFileBytes(string key)
        {
            if (_StorageType == FileStorageType.Sharepoint)
            {
                return await ReadFromSharepoint(key);
            }
            BlobData data = await _Blobs.GetStream(key); // Retrieve the blob data stream

            // Check if data and its properties are valid
            if (data?.Data?.CanRead == true && data.ContentLength > 0)
            {
                using (data.Data)
                using (var memoryStream = new MemoryStream())
                {
                    await data.Data.CopyToAsync(memoryStream); // Copy stream data to memory stream
                    return memoryStream.ToArray(); // Return byte array of file data
                }
            }
            return null; // Return null if data is not available or invalid
        }


        /// <summary>
        /// Reads all data from a stream and returns it as a byte array.
        /// </summary>
        /// <param name="stream">Input stream to read from.</param>
        /// <returns>Byte array of the stream data.</returns>
        private static byte[] ReadToEnd(Stream stream)
        {
            long originalPosition = 0;

            if (stream.CanSeek)
            {
                originalPosition = stream.Position; // Save original stream position
                stream.Position = 0; // Reset stream position to start
            }

            try
            {
                byte[] readBuffer = new byte[4096];
                int totalBytesRead = 0;
                int bytesRead;

                // Read data from the stream in chunks
                while ((bytesRead = stream.Read(readBuffer, totalBytesRead, readBuffer.Length - totalBytesRead)) > 0)
                {
                    totalBytesRead += bytesRead;

                    // If buffer is full, double its size
                    if (totalBytesRead == readBuffer.Length)
                    {
                        int nextByte = stream.ReadByte();
                        if (nextByte != -1)
                        {
                            byte[] temp = new byte[readBuffer.Length * 2];
                            Buffer.BlockCopy(readBuffer, 0, temp, 0, readBuffer.Length);
                            Buffer.SetByte(temp, totalBytesRead, (byte)nextByte);
                            readBuffer = temp;
                            totalBytesRead++;
                        }
                    }
                }

                // Create a buffer of the correct size and copy data into it
                byte[] buffer = readBuffer;
                if (readBuffer.Length != totalBytesRead)
                {
                    buffer = new byte[totalBytesRead];
                    Buffer.BlockCopy(readBuffer, 0, buffer, 0, totalBytesRead);
                }
                return buffer; // Return the final buffer
            }
            finally
            {
                // Restore the original position if the stream supports seeking
                if (stream.CanSeek)
                {
                    stream.Position = originalPosition;
                }
            }
        }
    }

    /// <summary>
    /// Represents an output parameter for database operations.
    /// </summary>
    public class OutParam
    {
        public int ReturnStatus { get; set; } // Status of the database operation
    }

    /// <summary>
    /// Represents file data retrieved from the database.
    /// </summary>
    public class FileData
    {
        public byte[] file_stream { get; set; } // Byte array of file stream data
    }
}
