﻿using System.Data.SqlClient;

namespace CaliberFileSystem.Utilities
{
    public interface ITableUpdater
    {
        /// <summary>
        /// Updates the database with the given key and metadata, marking as "CORRUPTED" if necessary.
        /// </summary>
        Task UpdateDatabaseAsyncDMS(SqlConnection connection, string updateQuery, string key, string docType, string fileExt, long indexId);

        /// <summary>
        /// Marks a record as corrupted in the database.
        /// </summary>
        Task MarkAsCorruptedAsync(SqlConnection connection, long indexId);

        /// <summary>
        /// Executes an update query on the specified table with the provided parameters.
        /// </summary>
        /// <param name="connection">The active SQL connection.</param>
        /// <param name="updateQuery">The SQL update query to execute.</param>
        /// <param name="parameters">A dictionary of parameter names and their values.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        Task ExecuteUpdateQueryAsync(SqlConnection connection, string updateQuery, Dictionary<string, object> parameters);

        /// <summary>
        /// Updates the database with the given key and metadata, marking as "CORRUPTED" if necessary.
        /// </summary>
        Task UpdateDatabaseLRNandASSRIQAsync(SqlConnection connection, string updateQuery, object docId);
    }
    public class TableUpdater : ITableUpdater
    {
        /// <summary>
        /// Updates the database with the given key and metadata, marking as "CORRUPTED" if necessary.
        /// </summary>
        public async Task UpdateDatabaseAsyncDMS(SqlConnection connection, string updateQuery, string key, string docType, string fileExt, long indexId)
        {
            try
            {

                var parameters = new Dictionary<string, object>
    {
        { "@Key", key },
        { "@DocType", string.IsNullOrEmpty(docType) ? (object)DBNull.Value : docType },
        { "@FileExt", string.IsNullOrEmpty(fileExt) ? (object)DBNull.Value : fileExt },
        { "@IndexId", indexId }
    };

                await ExecuteUpdateQueryAsync(connection, updateQuery, parameters);
            }
            catch (Exception ex) { }
        }

        /// <summary>
        /// Updates the database with the given key and metadata, marking as "CORRUPTED" if necessary.
        /// </summary>
        public async Task UpdateDatabaseLRNandASSRIQAsync(SqlConnection connection, string updateQuery, object docId)
        {
            try
            {
                var parameters = new Dictionary<string, object>
            {
                { "@Key", "CORRUPTED" },
                { "@DocId", docId }
            };

                await ExecuteUpdateQueryAsync(connection, updateQuery, parameters);
            }
            catch (Exception ex) { }
        }

        /// <summary>
        /// Marks a record as corrupted in the database.
        /// </summary>
        public async Task MarkAsCorruptedAsync(SqlConnection connection, long indexId)
        {
            try
            {
                var updateQuery = @"UPDATE [dbo].[LMS_DMS_INDEX] SET [LMS_BAL_FILESTORAGE_KEY] = @Key WHERE [LMS_DMS_INDEX_ID] = @IndexId";

                var parameters = new Dictionary<string, object>
    {
        { "@Key", "CORRUPTED" },
        { "@IndexId", indexId }
    };

                await ExecuteUpdateQueryAsync(connection, updateQuery, parameters);
            }
            catch (Exception ex) { }
        }

        /// <summary>
        /// Executes an update query on the specified table with the provided parameters.
        /// </summary>
        /// <param name="connection">The active SQL connection.</param>
        /// <param name="updateQuery">The SQL update query to execute.</param>
        /// <param name="parameters">A dictionary of parameter names and their values.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        public async Task ExecuteUpdateQueryAsync(SqlConnection connection, string updateQuery, Dictionary<string, object> parameters)
        {
            // Validate inputs
            if (connection == null)
                throw new ArgumentNullException(nameof(connection), "Connection cannot be null.");

            if (string.IsNullOrWhiteSpace(updateQuery))
                throw new ArgumentException("Update query cannot be null or whitespace.", nameof(updateQuery));

            if (parameters == null || parameters.Count == 0)
                throw new ArgumentException("Parameters cannot be null or empty.", nameof(parameters));

            try
            {
                // Create and configure the SQL command
                using (var command = new SqlCommand(updateQuery, connection))
                {
                    // Add parameters to the command
                    foreach (var param in parameters)
                    {
                        command.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                    }

                    // Execute the command asynchronously
                    int rowsAffected = await command.ExecuteNonQueryAsync();
                    if (rowsAffected == 0)
                    {
                        throw new InvalidOperationException("No record was updated.");
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                // Log or handle SQL-related exceptions
                throw new Exception("An error occurred while executing the update query.", sqlEx);
            }
            catch (Exception ex)
            {
                // Handle any other exceptions
                throw new Exception("An unexpected error occurred while executing the update query.", ex);
            }
        }
    }
}
