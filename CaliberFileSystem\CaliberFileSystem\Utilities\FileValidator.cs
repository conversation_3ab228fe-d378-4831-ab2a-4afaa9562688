﻿using System.Text;

namespace CaliberFileSystem.Utilities
{
    /// <summary>
    /// Interface for validating files based on their byte content and MIME type.
    /// </summary>
    public interface IFileValidator
    {
        /// <summary>
        /// Validates a file by checking its byte signature and MIME type.
        /// </summary>
        /// <param name="fileBytes">The byte array of the file to validate.</param>
        /// <param name="mimeType">The MIME type of the file.</param>
        /// <returns>True if the file is valid according to its MIME type; otherwise, false.</returns>
        bool ValidateFile(byte[] fileBytes, string mimeType);
    }

    /// <summary>
    /// Implementation of the file validator to check file integrity and type based on byte signatures.
    /// </summary>
    public class FileValidator : IFileValidator
    {
        /// <summary>
        /// Validates a file by checking its byte signature and MIME type.
        /// </summary>
        /// <param name="fileBytes">The byte array of the file to validate.</param>
        /// <param name="mimeType">The MIME type of the file.</param>
        /// <returns>True if the file matches the expected byte signature for the given MIME type; otherwise, false.</returns>
        public bool ValidateFile(byte[] fileBytes, string mimeType)
        {
            byte[] shortHeader = fileBytes.Take(4).ToArray();
            byte[] extendedHeader = fileBytes.Take(32).ToArray();

            return mimeType switch
            {
                "video/avi" => IsAvi(extendedHeader),
                "application/pdf" => IsPdf(shortHeader),
                "Application / pdf" => IsPdf(shortHeader),
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document" => IsDocx(shortHeader),
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" => IsXlsx(shortHeader),
                "application/vnd.openxmlformats-officedocument.presentationml.presentation" => IsPptx(shortHeader),
                "application/msword" => IsDoc(extendedHeader),
                "application/vnd.ms-excel" => IsXls(extendedHeader),
                "application/json" => IsJson(fileBytes),
                "text/html" => IsHtml(fileBytes),
                "application/xml" => IsXml(fileBytes),
                "image/jpeg" => IsJpeg(shortHeader),
                "image/png" => IsPng(shortHeader),
                "image/gif" => IsGif(shortHeader),
                "image/webp" => IsWebP(extendedHeader),
                "application/zip" => IsZip(extendedHeader),
                "application/x-zip-compressed" => IsZip(extendedHeader),
                "application/x-rar-compressed" => IsRar(shortHeader),
                "video/mp4" => IsMp4(extendedHeader),
                "audio/mpeg" => IsMp3(extendedHeader),
                "video/x-ms-wmv" => IsWmv(extendedHeader),
                "video/x-flv" => IsFlv(shortHeader),
                "video/mpeg" => IsMpeg(shortHeader),
                "video/quicktime" => IsMov(extendedHeader),
                "video/x-msvideo" => IsAvi(extendedHeader),
                "video/3gpp" => Is3gp(extendedHeader),
                "application/octet-stream" => true,
                "audio/wav" => IsWav(extendedHeader),
                "text/plain" => true,
                _ => false
            };
        }
        /// <summary>
        /// Checks if the file header matches the AVI file signature.
        /// </summary>
        private static bool IsAvi(byte[] header)
        {
            if (header.Length < 12) return false;

            // Check for 'RIFF' at the start and 'AVI ' at byte 8
            return header.Take(4).SequenceEqual(new byte[] { 0x52, 0x49, 0x46, 0x46 }) &&  // "RIFF"
                   header.Skip(8).Take(4).SequenceEqual(new byte[] { 0x41, 0x56, 0x49, 0x20 }); // "AVI "
        }

        /// <summary>
        /// Checks if the file header matches the PDF signature.
        /// </summary>
        private static bool IsPdf(byte[] header) => header.SequenceEqual(new byte[] { 0x25, 0x50, 0x44, 0x46 });

        /// <summary>
        /// Checks if the file header matches the DOCX signature.
        /// </summary>
        private static bool IsDocx(byte[] header) => header.SequenceEqual(new byte[] { 0x50, 0x4B, 0x03, 0x04 });

        /// <summary>
        /// Checks if the file header matches the XLS (Excel 97-2003) signature.
        /// </summary>
        private static bool IsXls(byte[] header)
        {
            // XLS files are Compound File Binary Format (CFBF) with this signature
            byte[] xlsSignature = new byte[] { 0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1 };

            // Ensure the header is long enough to check
            if (header.Length < xlsSignature.Length)
            {
                return false;
            }

            // Compare the bytes
            for (int i = 0; i < xlsSignature.Length; i++)
            {
                if (header[i] != xlsSignature[i])
                {
                    return false;
                }
            }
            return true;
        }


        /// <summary>
        /// Checks if the file header matches the XLSX signature.
        /// </summary>
        private static bool IsXlsx(byte[] header) => header.SequenceEqual(new byte[] { 0x50, 0x4B, 0x03, 0x04 });

        /// <summary>
        /// Checks if the file header matches the PPTX signature.
        /// </summary>
        private static bool IsPptx(byte[] header) => header.SequenceEqual(new byte[] { 0x50, 0x4B, 0x03, 0x04 });

        /// <summary>
        /// Checks if the file header matches the JPEG signature.
        /// </summary>
        private static bool IsJpeg(byte[] header) => header.Take(2).SequenceEqual(new byte[] { 0xFF, 0xD8 });

        /// <summary>
        /// Checks if the file header matches the PNG signature.
        /// </summary>
        private static bool IsPng(byte[] header) => header.SequenceEqual(new byte[] { 0x89, 0x50, 0x4E, 0x47 });

        /// <summary>
        /// Checks if the file header matches the GIF signature.
        /// </summary>
        private static bool IsGif(byte[] header) => header.SequenceEqual(new byte[] { 0x47, 0x49, 0x46, 0x38 });

        /// <summary>
        /// Checks if the file header matches the WebP signature.
        /// </summary>
        private static bool IsWebP(byte[] header)
        {
            if (header.Length < 12) return false;

            // Check for 'RIFF' at the start and 'WEBP' at byte 8
            return header.Take(4).SequenceEqual(new byte[] { 0x52, 0x49, 0x46, 0x46 }) &&  // "RIFF"
                   header.Skip(8).Take(4).SequenceEqual(new byte[] { 0x57, 0x45, 0x42, 0x50 }); // "WEBP"
        }

        /// <summary>
        /// Checks if the file header matches the ZIP signature by looking at an extended header.
        /// </summary>
        private static bool IsZip(byte[] header)
        {
            if (header.Length < 32) return false;
            // Check for 'PK' signature at the beginning
            return header.Take(4).SequenceEqual(new byte[] { 0x50, 0x4B, 0x03, 0x04 }) ||
                   header.Take(4).SequenceEqual(new byte[] { 0x50, 0x4B, 0x05, 0x06 }) || // Empty archive
                   header.Take(4).SequenceEqual(new byte[] { 0x50, 0x4B, 0x07, 0x08 });   // Spanned archive
        }

        /// <summary>
        /// Checks if the file header matches the RAR signature.
        /// </summary>
        private static bool IsRar(byte[] header) => header.SequenceEqual(new byte[] { 0x52, 0x61, 0x72, 0x21 });

        /// <summary>
        /// Checks if the file header matches the MP3 signature by searching for ID3 or frame headers.
        /// </summary>
        private static bool IsMp3(byte[] header)
        {
            if (header.Length < 32) return false;
            // Check for ID3 tag at the start or potential frame headers.
            return header.Take(3).SequenceEqual(new byte[] { 0x49, 0x44, 0x33 }) || // "ID3"
                   (header[0] == 0xFF && (header[1] & 0xE0) == 0xE0); // Frame sync (first 11 bits set)
        }

        /// <summary>
        /// Checks if the file header matches the MP4 signature by searching for the 'ftyp' identifier.
        /// </summary>
        private static bool IsMp4(byte[] header)
        {
            if (header.Length < 32) return false;
            // Look for 'ftyp' in the first 32 bytes
            for (int i = 4; i < header.Length - 4; i++)
            {
                if (header[i] == 0x66 && header[i + 1] == 0x74 && header[i + 2] == 0x79 && header[i + 3] == 0x70)
                {
                    return true;
                }
            }
            return false;
        }


        /// <summary>
        /// Checks if the file header matches the WMV (ASF) signature by using the extended header.
        /// </summary>
        private static bool IsWmv(byte[] header)
        {
            if (header.Length < 32) return false;
            // Check for ASF GUID signature at the start
            return header.Take(16).SequenceEqual(new byte[]
            {
        0x30, 0x26, 0xB2, 0x75, 0x8E, 0x66, 0xCF, 0x11,
        0xA6, 0xD9, 0x00, 0xAA, 0x00, 0x62, 0xCE, 0x6C
            });
        }

        /// <summary>
        /// Checks if the file header matches the flv signature.
        /// </summary>
        private static bool IsFlv(byte[] header)
        {
            if (header.Length < 4) return false;

            // Check for 'FLV' signature
            return header.Take(3).SequenceEqual(new byte[] { 0x46, 0x4C, 0x56 }) &&  // "FLV"
                   (header[3] == 0x01 || header[3] == 0x00); // version (usually 1 or 0)
        }

        /// <summary>
        /// Checks if the file header matches the mpeg signature.
        /// </summary>
        private static bool IsMpeg(byte[] header) =>
            header.Length >= 4 &&
            (header.Take(4).SequenceEqual(new byte[] { 0x00, 0x00, 0x01, 0xBA }) ||
             header.Take(4).SequenceEqual(new byte[] { 0x00, 0x00, 0x01, 0xB3 }));

        /// <summary>
        /// Checks if the file header matches the MOV signature using an extended header.
        /// </summary>
        private static bool IsMov(byte[] header)
        {
            if (header.Length < 12) return false; // Ensure we have at least 12 bytes for a valid MOV check.

            // Check if 'ftyp' is at a reasonable offset (e.g., at byte 4 or 8)
            for (int i = 4; i <= header.Length - 4; i++)
            {
                if (header[i] == 0x66 && header[i + 1] == 0x74 && header[i + 2] == 0x79 && header[i + 3] == 0x70)
                {
                    return true; // Found 'ftyp' indicating a MOV/QuickTime file.
                }
            }
            return false;
        }


        /// <summary>
        /// Checks if the file header matches the 3gp signature.
        /// </summary>
        private static bool Is3gp(byte[] header)
        {
            if (header.Length < 12) return false;

            // Look for 'ftyp' followed by '3gp' markers
            return header.Skip(4).Take(4).SequenceEqual(new byte[] { 0x66, 0x74, 0x79, 0x70 }) &&
                   (header.Skip(8).Take(3).SequenceEqual(new byte[] { 0x33, 0x67, 0x70 }) ||   // "3gp"
                    header.Skip(8).Take(4).SequenceEqual(new byte[] { 0x33, 0x67, 0x70, 0x34 }) || // "3gp4"
                    header.Skip(8).Take(4).SequenceEqual(new byte[] { 0x33, 0x67, 0x70, 0x35 }));  // "3gp5"
        }

        /// <summary>
        /// Checks if the file header matches the WAV signature.
        /// </summary>
        private static bool IsWav(byte[] header)
        {
            if (header.Length < 12) return false;

            // Check for 'RIFF' followed by 'WAVE'
            return header.Take(4).SequenceEqual(new byte[] { 0x52, 0x49, 0x46, 0x46 }) &&  // "RIFF"
                   header.Skip(8).Take(4).SequenceEqual(new byte[] { 0x57, 0x41, 0x56, 0x45 }); // "WAVE"
        }

        /// <summary>
        /// Checks if the file header matches the DOC (Microsoft Word 97-2003) signature.
        /// </summary>
        private static bool IsDoc(byte[] header)
        {
            // DOC files are in Compound File Binary Format (CFBF) with this signature
            byte[] docSignature = new byte[] { 0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1 };

            // Ensure the header is long enough to check
            if (header.Length < docSignature.Length)
            {
                return false;
            }

            // Compare the bytes
            for (int i = 0; i < docSignature.Length; i++)
            {
                if (header[i] != docSignature[i])
                {
                    return false;
                }
            }
            return true;
        }


        /// <summary>
        /// Checks if the file content represents JSON format by inspecting the first few characters.
        /// </summary>
        private static bool IsJson(byte[] fileBytes)
        {
            string content = Encoding.UTF8.GetString(fileBytes).TrimStart();
            return content.StartsWith("{") || content.StartsWith("[");
        }

        /// <summary>
        /// Checks if the file content represents HTML format by inspecting the first few characters.
        /// </summary>
        private static bool IsHtml(byte[] fileBytes)
        {
            string content = Encoding.UTF8.GetString(fileBytes).TrimStart();
            return content.StartsWith("<!DOCTYPE html>") || content.StartsWith("<html>");
        }

        /// <summary>
        /// Checks if the file content represents XML format by inspecting the first few characters.
        /// </summary>
        private static bool IsXml(byte[] fileBytes)
        {
            string content = Encoding.UTF8.GetString(fileBytes).TrimStart();
            return content.StartsWith("<?xml");
        }
    }
}
