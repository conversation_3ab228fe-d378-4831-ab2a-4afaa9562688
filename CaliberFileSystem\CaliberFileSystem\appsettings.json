{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DBCon": "Server=CQMPRDTNE01\\MSSQLSERVER01; Initial Catalog=CaliberFileStorage; Trusted_Connection=true;TrustServerCertificate=True;MultiSubnetFailover=True; MultipleActiveResultSets=true"}, "Jwt": {"Issuer": "http://localhost:5049/", "Audience": "http://localhost:5049/", "Key": "etjroiertkdfngkneiurehvnjefnvk,sdmlasjfdoiwehfiuhfgbjbdsfbwenrnwlkejtlasmdA.EJQOIIW2J423K5N3T JSDBFHJSERIUQ3HR2H345BHJ34N5K34H5B34KTNEKJRMGBERNGKERJNGKEJNGJERTKEJBJDKFNBLKDFVLK"}, "AllowedOrigins": ["http://localhost"], "StorageOptions": {"Enabled": true, "StorageType": "Disk", "ServiceType": "Blob", "Provider": "<azure/aws/minio>", "AzureSettings": {"AccountName": "<ACCOUNT_NAME>", "AccessKey": "<ACCESS_KEY>", "Endpoint": "https://[accountname].blob.core.windows.net/", "Container": "<CONTAINER>"}, "AwsS3Settings": {"Endpoint": "<endpoint>", "AccessKey": "<ACCESS_KEY>", "SecretKey": "<SECRET_KEY", "Region": "<REGION>", "Bucket": "<BUCKET>", "SSL": true, "BaseUrl": "<BaseUrl>"}, "DiskSettings": {"Directory": "C:\\Temp\\FileStorage"}, "SharepointSettings": {"ClientId": "4b226b1a-9633-4694-961d-d6487de85e32", "ClientSecret": "****************************************", "TenantId": "5a69283a-cd30-43ac-aca1-d624657bee23", "DriveId": "b!NzCW243VsE2iaHIMfdbzc8aMyx5LP5hLm5AlUeFI3SiAEUMRJ7sJSIHSVeGVCScz"}, "KomodoSettings": {"Endpoint": "<endpoint>", "IndexGUID": "<IndexGUID>", "APIkey": "<APIkey>"}, "KvpbaseSettings": {"Endpoint": "<endpoint>", "UserGUID": "<UserGUID>", "Container": "<CONTAINER>", "APIkey": "<APIkey>"}}, "AllowedHosts": "*", "IronPdfLicensekey": "IRONPDF.CALIBERTECHNOLOGIESPRIVATELIMITED.IRO240423.9673.93113-A4D265D263-DYAKUX7YGM3JKIE-MDTALOG6RHPR-OGCSXFSRA6YL-YTOY5CUHSOFJ-3QT5V7CQIXOE-CG4EIA-L7H7BKYJDR6VEA-IRONPDF.DOTNET.PLUS.SAAS.OEM.5YR-GBR3SA.RENEW.SUPPORT.22.APR.2029", "windowsauntentation": 1}