﻿namespace CaliberFileSystem.Utilities
{
    /// <summary>
    /// Interface for PDF flattening service.
    /// Provides a method to flatten a PDF document by removing unnecessary layers
    /// and compressing the file without compromising its quality.
    /// </summary>
    public interface IPdfFlattner
    {
        /// <summary>
        /// Flattens a PDF file to optimize its size and content.
        /// </summary>
        /// <param name="uploadedPDF">The byte array representing the uploaded PDF document.</param>
        /// <returns>A task representing the asynchronous operation, with a byte array containing the flattened PDF.</returns>
        ValueTask<byte[]> FlatteningPDF(byte[] uploadedPDF);

        /// <summary>
        /// Compresses a PDF file to reduce its size.
        /// </summary>
        /// <param name="uploadedPDF">The byte array representing the uploaded PDF document.</param>
        /// <returns>A task representing the asynchronous operation, with a byte array containing the compressed PDF.</returns>
        ValueTask<byte[]> CompressPDF(byte[] uploadedPDF);
    }

    /// <summary>
    /// Service class for flattening PDF files.
    /// Implements the IPdfFlattner interface to provide functionality
    /// for optimizing and compressing PDF files.
    /// </summary>
    public class PdfFlattner : IPdfFlattner
    {
        private static readonly string IronPdfLicenseKey = "IRONPDF.CALIBERTECHNOLOGIESPRIVATELIMITED.IRO240423.9673.93113-A4D265D263-DYAKUX7YGM3JKIE-MDTALOG6RHPR-OGCSXFSRA6YL-YTOY5CUHSOFJ-3QT5V7CQIXOE-CG4EIA-L7H7BKYJDR6VEA-IRONPDF.DOTNET.PLUS.SAAS.OEM.5YR-GBR3SA.RENEW.SUPPORT.22.APR.2029";

        /// <summary>
        /// Flattens the given PDF file to reduce its size while maintaining its quality.
        /// Uses IronPDF to process the PDF file and remove unnecessary layers.
        /// </summary>
        /// <param name="uploadedPDF">The byte array representing the uploaded PDF document.</param>
        /// <returns>A task that represents the asynchronous flattening operation. The task result contains the byte array of the flattened PDF file.</returns>
        public async ValueTask<byte[]> FlatteningPDF(byte[] uploadedPDF)
        {
            try
            {
                // Set the license key for IronPDF to authorize the usage of the library
                License.LicenseKey = IronPdfLicenseKey;

                // Load the PDF document from the provided byte array
                using (PdfDocument PDF = new PdfDocument(uploadedPDF))
                {
                    // The Flatten method removes any unnecessary layers from the PDF
                    // and compresses it to reduce the file size while maintaining quality
                    PDF.Flatten();

                    // Return the processed (flattened) PDF as a byte array
                    return PDF.BinaryData;
                }
            }
            catch (Exception ex)
            {
                // Log the exception details to the console for debugging purposes
                Console.WriteLine($"An error occurred while flattening the PDF: {ex.Message}");

                // Return null to indicate that an error occurred during processing
                return null;
            }
        }
        public async ValueTask<byte[]> CompressPDF(byte[] uploadedPDF)
        {
            try
            {
                // Set the license key for IronPDF to authorize the usage of the library
                License.LicenseKey = IronPdfLicenseKey;

                // Load the PDF document from the provided byte array
                using (PdfDocument PDF = new PdfDocument(uploadedPDF))
                {
                    // Compress the PDF by optimizing images and reducing unused elements
                    PDF.Compress();

                    // Return the compressed PDF as a byte array
                    return PDF.BinaryData;
                }
            }
            catch (Exception ex)
            {
                // Log the exception details to the console for debugging purposes
                Console.WriteLine($"An error occurred while compressing the PDF: {ex.Message}");

                // Return null to indicate that an error occurred during processing
                return null;
            }
        }

    }
}