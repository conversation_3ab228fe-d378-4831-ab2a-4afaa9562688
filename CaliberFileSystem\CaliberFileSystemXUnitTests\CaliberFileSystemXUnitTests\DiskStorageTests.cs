using BlobHelper;
using Caliber.DataAccess;
using CaliberFileSystem.Models;
using CaliberFileSystem.Services;
using Microsoft.Extensions.Configuration;

namespace CaliberFileSystemXUnitTests
{
    public class DiskStorageTests
    {
        private IConfiguration configuration;
        [Fact]
        public async Task WriteFileToDisk()
        {
          
            //Arrange
            StorageHelperService storageHelperService = new StorageHelperService(new BlobClient(new DiskSettings("C:\\Temp\\FileStorage")), FileStorageType.Disk, new CommonDataAccessAsync("Server=CQMPRDTNE01\\MSSQLSERVER01; Initial Catalog=CaliberFileStorage; Trusted_Connection=true;TrustServerCertificate=True;MultiSubnetFailover=True; MultipleActiveResultSets=true"), configuration);
            byte[] byteArray = File.ReadAllBytes("C:\\Temp\\TestFiles\\xmlresult2.xml");

            // Act
            await storageHelperService.Write("", byteArray);

            //// Assert
            //Assert.IsType<OkResult>();
        }
    }
}